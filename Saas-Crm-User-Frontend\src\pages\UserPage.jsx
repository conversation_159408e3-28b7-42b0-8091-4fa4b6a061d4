// import React, { useState } from 'react';
// import { useNavigate } from 'react-router-dom';

// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import {
//     Table,
//     TableHeader,
//     TableBody,
//     TableRow,
//     TableHead,
//     TableCell
// } from '@/components/ui/table';

// import {
//     AlertDialog,
//     AlertDialogTrigger,
//     AlertDialogContent,
//     AlertDialogHeader,
//     AlertDialogTitle,
//     AlertDialogFooter,
//     AlertDialogCancel,
//     AlertDialogAction
// } from '@/components/ui/alert-dialog';

// import { Pencil, Trash2 } from 'lucide-react';


// export default function UserPage() {
//     const navigate = useNavigate();
//     const [users, setUsers] = useState([
//         { id: 1, name: '<PERSON><PERSON><PERSON>', email: 'qade<PERSON>@example.com' },
//         { id: 2, name: '<PERSON>', email: '<EMAIL>' }
//     ]);

//     const handleDeleteUser = (id) => {
//         setUsers(users.filter(user => user.id !== id));
//     };

//     return (
//         <Card className="p-4 rounded-none">
//             <CardHeader className="flex justify-between items-center">
//                 <CardTitle>User Management</CardTitle>
//                 <Button onClick={() => navigate('/add')}>Add User</Button>
//             </CardHeader>

//             <CardContent>
//                 <Table>
//                     <TableHeader>
//                         <TableRow>
//                             <TableHead>#</TableHead>
//                             <TableHead>Name</TableHead>
//                             <TableHead>Email</TableHead>
//                             <TableHead className="text-right">Actions</TableHead>
//                         </TableRow>
//                     </TableHeader>
//                     <TableBody>
//                         {users.map((user, index) => (
//                             <TableRow key={user.id}>
//                                 <TableCell>{index + 1}</TableCell>
//                                 <TableCell>{user.name}</TableCell>
//                                 <TableCell>{user.email}</TableCell>
//                                 <TableCell className="text-right space-x-2">
//                                     <Button
//                                         variant="outline"
//                                         size="sm"
//                                         onClick={() => navigate(`/edit/${user.id}`)}
//                                     >
//                                         <Pencil className="h-4 w-4" />
//                                     </Button>

//                                     <AlertDialog>
//                                         <AlertDialogTrigger asChild>
//                                             <Button variant="destructive" size="sm">
//                                                 <Trash2 className="h-4 w-4" />
//                                             </Button>
//                                         </AlertDialogTrigger>
//                                         <AlertDialogContent>
//                                             <AlertDialogHeader>
//                                                 <AlertDialogTitle>Are you sure?</AlertDialogTitle>
//                                             </AlertDialogHeader>
//                                             <p>This action cannot be undone.</p>
//                                             <AlertDialogFooter>
//                                                 <AlertDialogCancel>Cancel</AlertDialogCancel>
//                                                 <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
//                                                     Delete
//                                                 </AlertDialogAction>
//                                             </AlertDialogFooter>
//                                         </AlertDialogContent>
//                                     </AlertDialog>
//                                 </TableCell>
//                             </TableRow>
//                         ))}
//                     </TableBody>
//                 </Table>
//             </CardContent>
//         </Card>
//     );
// }


import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2 } from 'lucide-react';

export default function UserPage() {
    const [users, setUsers] = useState([
        { id: 1, name: 'Qadeer', email: '<EMAIL>' },
        { id: 2, name: 'Hasan', email: '<EMAIL>' }
    ]);

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [currentUser, setCurrentUser] = useState(null);
    const [form, setForm] = useState({ name: '', email: '' });

    // Add
    const handleAddUser = () => {
        const newUser = {
            id: Date.now(),
            name: form.name,
            email: form.email
        };
        setUsers([...users, newUser]);
        setForm({ name: '', email: '' });
        setShowAdd(false);
    };

    // Edit
    const handleEditUser = () => {
        setUsers(
            users.map(user => user.id === currentUser.id ? { ...user, ...form } : user)
        );
        setShowEdit(false);
        setForm({ name: '', email: '' });
    };

    // Delete
    const handleDeleteUser = id => {
        setUsers(users.filter(user => user.id !== id));
    };

    // Open edit modal
    const openEditModal = (user) => {
        setCurrentUser(user);
        setForm({ name: user.name, email: user.email });
        setShowEdit(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>User Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button>Add User</Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add User</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label className="pb-3">Name</Label>
                                <Input value={form.name} onChange={e => setForm({ ...form, name: e.target.value })} />
                            </div>
                            <div>
                                <Label className="pb-3">Email</Label>
                                <Input value={form.email} onChange={e => setForm({ ...form, email: e.target.value })} />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button onClick={handleAddUser}>Add</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {users.map((user, index) => (
                            <TableRow key={user.id}>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{user.name}</TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell className="text-right space-x-2">
                                    <Dialog open={showEdit && currentUser?.id === user.id} onOpenChange={setShowEdit}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => openEditModal(user)}
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Edit User</DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label className="pb-3">Name</Label>
                                                    <Input
                                                        value={form.name}
                                                        onChange={e => setForm({ ...form, name: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label className="pb-3">Email</Label>
                                                    <Input
                                                        value={form.email}
                                                        onChange={e => setForm({ ...form, email: e.target.value })}
                                                    />
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button onClick={handleEditUser}>Update</Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="destructive" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                            </AlertDialogHeader>
                                            <p>This action cannot be undone.</p>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
                                                    Delete
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}
