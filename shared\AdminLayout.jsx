import { useState, createContext, useContext } from 'react';
import {
    Menu,
    X,
    User,
    LayoutDashboard,
    Wallet,
    Settings,
    Shield,
    FileText,
    Search,
    Bell,
    ChevronDown,
    LogOut
} from 'lucide-react';

// Layout Context for managing global layout state
const LayoutContext = createContext();

export const useLayout = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayout must be used within a LayoutProvider');
    }
    return context;
};

// Layout Provider Component
function LayoutProvider({ children }) {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
    const [currentPage, setCurrentPage] = useState('Dashboard');

    const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
    const toggleSidebarCollapse = () => setIsSidebarCollapsed(!isSidebarCollapsed);
    const closeSidebar = () => setIsSidebarOpen(false);

    const value = {
        isSidebarOpen,
        isSidebarCollapsed,
        currentPage,
        toggleSidebar,
        toggleSidebarCollapse,
        closeSidebar,
        setCurrentPage
    };

    return (
        <LayoutContext.Provider value={value}>
            {children}
        </LayoutContext.Provider>
    );
}

/**
 * Demo App component to showcase AdminLayout usage
 */
function App() {
    const UserDashboardContent = () => (
        <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h1 className="text-3xl font-bold mb-4 text-gray-800">User Dashboard</h1>
                <p className="text-gray-600 mb-8">
                    Welcome to your admin dashboard. Monitor your key metrics and manage your application.
                </p>

                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <MetricCard
                        icon={<User className="text-blue-500" size={24} />}
                        title="Total Users"
                        value="1,234"
                        change="+12% from last month"
                        changeType="positive"
                    />
                    <MetricCard
                        icon={<Wallet className="text-green-500" size={24} />}
                        title="Revenue"
                        value="$56,789"
                        change="+8% from last quarter"
                        changeType="positive"
                    />
                    <MetricCard
                        icon={<FileText className="text-orange-500" size={24} />}
                        title="New Leads"
                        value="250"
                        change="+20 new today"
                        changeType="positive"
                    />
                </div>
            </div>
        </div>
    );

    return (
        <AdminLayout>
            <UserDashboardContent />
        </AdminLayout>
    );
}

// Metric Card Component
function MetricCard({ icon, title, value, change, changeType = 'neutral' }) {
    const changeColorClass = {
        positive: 'text-green-600',
        negative: 'text-red-600',
        neutral: 'text-gray-500'
    }[changeType];

    return (
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center space-x-4 mb-4">
                {icon}
                <h2 className="text-xl font-semibold text-gray-700">{title}</h2>
            </div>
            <p className="text-4xl font-bold text-gray-900 mb-2">{value}</p>
            <p className={`text-sm ${changeColorClass}`}>{change}</p>
        </div>
    );
}

// Sidebar Component
function Sidebar({ navLinks: customNavLinks, onLogout }) {
    const { isSidebarOpen, isSidebarCollapsed, closeSidebar, currentPage, setCurrentPage } = useLayout();

    const defaultNavLinks = [
        { name: 'Dashboard', icon: LayoutDashboard, href: '#', id: 'dashboard' },
        { name: 'Users', icon: User, href: '#', id: 'users' },
        { name: 'Leads', icon: FileText, href: '#', id: 'leads' },
        { name: 'Subscriptions', icon: Wallet, href: '#', id: 'subscriptions' },
        { name: 'Settings', icon: Settings, href: '#', id: 'settings' },
        { name: 'Roles', icon: Shield, href: '#', id: 'roles' },
    ];

    const navLinks = customNavLinks || defaultNavLinks;

    const handleNavClick = (linkName) => {
        setCurrentPage(linkName);
        closeSidebar();
    };

    return (
        <>
            {/* Mobile Sidebar Overlay */}
            {isSidebarOpen && (
                <div
                    className="fixed inset-0 z-30 bg-gray-900 bg-opacity-50 lg:hidden"
                    onClick={closeSidebar}
                />
            )}

            {/* Sidebar */}
            <aside
                className={`
                    fixed inset-y-0 left-0 z-40 bg-white shadow-xl transition-all duration-300 ease-in-out
                    ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                    ${isSidebarCollapsed ? 'w-16' : 'w-64'}
                    lg:translate-x-0 lg:static lg:inset-y-auto lg:flex-shrink-0 lg:shadow-lg
                `}
            >
                <div className="h-full flex flex-col">
                    {/* Sidebar Header */}
                    <div className="p-4 flex items-center justify-between border-b border-gray-200 bg-gradient-to-r from-indigo-600 to-purple-600">
                        <div className={`flex items-center space-x-3 ${isSidebarCollapsed ? 'justify-center' : ''}`}>
                            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                                <LayoutDashboard className="w-5 h-5 text-indigo-600" />
                            </div>
                            {!isSidebarCollapsed && (
                                <span className="text-xl font-bold text-white">SaaS CRM</span>
                            )}
                        </div>
                        <button
                            className="lg:hidden text-white hover:text-gray-200 transition-colors"
                            onClick={closeSidebar}
                        >
                            <X size={20} />
                        </button>
                    </div>

                    {/* Navigation */}
                    <nav className="flex-1 p-4 overflow-y-auto">
                        <ul className="space-y-2">
                            {navLinks.map((link) => {
                                const IconComponent = link.icon;
                                const isActive = currentPage === link.name;

                                return (
                                    <li key={link.id}>
                                        <button
                                            onClick={() => handleNavClick(link.name)}
                                            className={`
                                                w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200
                                                ${isActive
                                                    ? 'bg-indigo-50 text-indigo-600 border-r-2 border-indigo-600'
                                                    : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'
                                                }
                                                ${isSidebarCollapsed ? 'justify-center' : ''}
                                            `}
                                        >
                                            <IconComponent size={20} />
                                            {!isSidebarCollapsed && (
                                                <span className="font-medium">{link.name}</span>
                                            )}
                                        </button>
                                    </li>
                                );
                            })}
                        </ul>
                    </nav>

                    {/* Sidebar Footer */}
                    <div className="p-4 border-t border-gray-200">
                        <button
                            onClick={() => onLogout && onLogout()}
                            className={`
                                w-full flex items-center space-x-3 p-3 rounded-lg text-gray-700
                                hover:bg-red-50 hover:text-red-600 transition-colors duration-200
                                ${isSidebarCollapsed ? 'justify-center' : ''}
                            `}
                        >
                            <LogOut size={20} />
                            {!isSidebarCollapsed && <span className="font-medium">Logout</span>}
                        </button>
                    </div>
                </div>
            </aside>
        </>
    );
}

// Header Component
function Header() {
    const { toggleSidebar, toggleSidebarCollapse, currentPage, isSidebarCollapsed } = useLayout();

    return (
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-20">
            <div className="flex items-center justify-between px-4 py-3 lg:px-6">
                {/* Left Section */}
                <div className="flex items-center space-x-4">
                    <button
                        onClick={toggleSidebar}
                        className="lg:hidden p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                        <Menu size={20} />
                    </button>

                    <button
                        onClick={toggleSidebarCollapse}
                        className="hidden lg:flex p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                        <Menu size={20} />
                    </button>

                    <div className="flex flex-col">
                        <h1 className="text-xl font-semibold text-gray-800">{currentPage}</h1>
                        <p className="text-sm text-gray-500">Welcome back, Admin</p>
                    </div>
                </div>

                {/* Right Section */}
                <div className="flex items-center space-x-4">
                    {/* Search Bar */}
                    <div className="relative hidden md:block">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                        <input
                            type="text"
                            placeholder="Search..."
                            className="pl-10 pr-4 py-2 w-64 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 transition-colors"
                        />
                    </div>

                    {/* Notifications */}
                    <button className="relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                        <Bell size={20} />
                        <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                    </button>

                    {/* User Menu */}
                    <div className="flex items-center space-x-3">
                        <div className="hidden md:block text-right">
                            <p className="text-sm font-medium text-gray-700">Admin User</p>
                            <p className="text-xs text-gray-500"><EMAIL></p>
                        </div>
                        <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                AU
                            </div>
                            <ChevronDown size={16} className="text-gray-500 hidden md:block" />
                        </button>
                    </div>
                </div>
            </div>
        </header>
    );
}

// Footer Component
function Footer() {
    return (
        <footer className="bg-white border-t border-gray-200 px-6 py-4">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
                <div className="text-sm text-gray-500">
                    &copy; 2025 SaaS CRM. All rights reserved.
                </div>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <a href="#" className="hover:text-indigo-600 transition-colors">Privacy Policy</a>
                    <a href="#" className="hover:text-indigo-600 transition-colors">Terms of Service</a>
                    <a href="#" className="hover:text-indigo-600 transition-colors">Support</a>
                </div>
            </div>
        </footer>
    );
}

// Main Content Wrapper
function MainContent({ children }) {
    const { isSidebarCollapsed } = useLayout();

    return (
        <div className={`
            flex-1 flex flex-col transition-all duration-300 ease-in-out
            ${isSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}
        `}>
            <Header />
            <main className="flex-1 overflow-y-auto bg-gray-50">
                <div className="p-6">
                    {children}
                </div>
            </main>
            <Footer />
        </div>
    );
}

/**
 * New AdminLayout Component with improved structure
 * Features:
 * - Modular component architecture
 * - Context-based state management
 * - Collapsible sidebar
 * - Responsive design
 * - Better accessibility
 * - Modern UI design
 */
function AdminLayout({ children, navLinks, onLogout }) {
    return (
        <LayoutProvider>
            <div className="flex min-h-screen bg-gray-50 font-sans antialiased">
                <Sidebar navLinks={navLinks} onLogout={onLogout} />
                <MainContent>
                    {children}
                </MainContent>
            </div>
        </LayoutProvider>
    );
}

export default AdminLayout;
