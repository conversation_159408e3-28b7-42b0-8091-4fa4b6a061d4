import { useState } from 'react';
import {
    Menu,
    X,
    User,
    LayoutDashboard,
    Wallet,
    Settings,
    Shield,
    FileText
} from 'lucide-react';

/**
 * Main App component to demonstrate the use of AdminLayout.
 * This component acts as the entry point and provides a simple content component
 * to be rendered inside the layout.
 */
export default function App() {
    /**
     * This is a placeholder component for the content that would be unique to a
     * microservice. The content inside this component will be rendered inside
     * the AdminLayout.
     */
    const UserDashboardContent = () => (
        <div className="p-6 bg-white rounded-xl shadow-lg border border-gray-200">
            <h1 className="text-3xl font-bold mb-4 text-gray-800">User Dashboard</h1>
            <p className="text-gray-700">This is the main content area for the user-specific part of the application. It is being rendered as the 'children' of the AdminLayout component.</p>
            <div className="mt-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 shadow-sm">
                    <div className="flex items-center space-x-4 mb-4">
                        <User className="text-indigo-500" size={24} />
                        <h2 className="text-xl font-semibold text-gray-700">Total Users</h2>
                    </div>
                    <p className="text-4xl font-bold text-gray-900">1,234</p>
                    <p className="text-sm text-gray-500 mt-1">+12% from last month</p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 shadow-sm">
                    <div className="flex items-center space-x-4 mb-4">
                        <Wallet className="text-green-500" size={24} />
                        <h2 className="text-xl font-semibold text-gray-700">Revenue</h2>
                    </div>
                    <p className="text-4xl font-bold text-gray-900">$56,789</p>
                    <p className="text-sm text-gray-500 mt-1">+8% from last quarter</p>
                </div>
                <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 shadow-sm">
                    <div className="flex items-center space-x-4 mb-4">
                        <FileText className="text-orange-500" size={24} />
                        <h2 className="text-xl font-semibold text-gray-700">New Leads</h2>
                    </div>
                    <p className="text-4xl font-bold text-gray-900">250</p>
                    <p className="text-sm text-gray-500 mt-1">+20 new today</p>
                </div>
            </div>
        </div>
    );

    return (
        <AdminLayout>
            <UserDashboardContent />
        </AdminLayout>
    );
}

/**
 * A reusable AdminLayout component that provides a consistent header, sidebar, and footer.
 * This component is designed to be the single source of truth for your UI shell.
 * It handles all the layout logic, so the child components should only focus on
 * rendering their unique content (e.g., tables, forms, charts).
 *
 * @param {object} props The component's props.
 * @param {React.ReactNode} props.children The content to be rendered inside the layout.
 */
function AdminLayout({ children }) {
    // State to manage the visibility of the sidebar on mobile devices.
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    // Function to toggle the sidebar's open state.
    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    // Define the navigation links for the sidebar.
    const navLinks = [
        { name: 'Dashboard', icon: <LayoutDashboard size={20} />, href: '#' },
        { name: 'Users', icon: <User size={20} />, href: '#' },
        { name: 'Leads', icon: <FileText size={20} />, href: '#' },
        { name: 'Subscriptions', icon: <Wallet size={20} />, href: '#' },
        { name: 'Settings', icon: <Settings size={20} />, href: '#' },
        { name: 'Roles', icon: <Shield size={20} />, href: '#' },
    ];

    return (
        <div className="flex flex-col min-h-screen font-sans antialiased bg-gray-100">
            {/* Mobile Sidebar Overlay */}
            {isSidebarOpen && (
                <div
                    className="fixed inset-0 z-30 bg-gray-900 bg-opacity-50 lg:hidden"
                    onClick={toggleSidebar}
                ></div>
            )}

            <div className="flex flex-1">
                {/* Sidebar component */}
                <aside
                    className={`fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-xl transition-transform duration-300 ease-in-out ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
                        } lg:translate-x-0 lg:static lg:inset-y-auto lg:w-64 lg:flex-shrink-0 lg:shadow-none`}
                >
                    <div className="h-full flex flex-col">
                        {/* Sidebar Header with app name */}
                        <div className="p-6 flex items-center justify-between border-b border-gray-200">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-extrabold text-indigo-600">SaaS CRM</span>
                            </div>
                            {/* Close button for mobile sidebar */}
                            <button className="lg:hidden text-gray-500 hover:text-gray-900" onClick={toggleSidebar}>
                                <X size={24} />
                            </button>
                        </div>
                        {/* Sidebar Navigation */}
                        <nav className="flex-1 p-4 overflow-y-auto">
                            <ul className="space-y-2">
                                {navLinks.map((link) => (
                                    <li key={link.name}>
                                        <a
                                            href={link.href}
                                            className="flex items-center space-x-3 p-3 rounded-lg text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200"
                                        >
                                            {link.icon}
                                            <span className="text-lg font-medium">{link.name}</span>
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </nav>
                    </div>
                </aside>

                {/* Main Content Area, including header and dynamic content */}
                <div className="flex-1 flex flex-col transition-all duration-300 ease-in-out lg:ml-64">
                    {/* Header component */}
                    <header className="flex items-center justify-between p-4 bg-white shadow-md z-10 lg:pl-8">
                        <div className="flex items-center space-x-4">
                            {/* Mobile menu toggle button */}
                            <button
                                onClick={toggleSidebar}
                                className="lg:hidden p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none"
                            >
                                <Menu size={24} />
                            </button>
                            <h1 className="text-2xl font-bold text-gray-800">Admin Dashboard</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <div className="relative hidden md:block">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="pl-4 pr-10 py-2 w-full rounded-full border border-gray-300 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 transition-colors"
                                />
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-5 w-5 absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    />
                                </svg>
                            </div>
                            <div className="flex items-center space-x-2">
                                <span className="text-gray-700 font-medium hidden md:block">Admin User</span>
                                <div className="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
                                    AU
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* The main content that changes per project. This is the 'children' prop. */}
                    <main className="flex-1 overflow-y-auto p-6">
                        {children}
                    </main>

                    {/* Footer component */}
                    <footer className="w-full text-center p-4 text-gray-500 text-sm border-t bg-white">
                        <p>&copy; 2025 SaaS CRM. All rights reserved.</p>
                    </footer>
                </div>
            </div>
        </div>
    );
}
